#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速CAN接口测试脚本
作者: ThingsBoard Gateway CAN测试
用途: 快速测试CAN接口并发送消息
"""

import can
import time
import struct

def quick_test():
    """快速测试CAN接口，持续发送测试帧，按 Ctrl+C 停止"""
    print("🚗 快速CAN接口测试")
    print("=" * 30)

    bus = None
    try:
        # 连接到CAN接口
        bus = can.interface.Bus(channel='vcan0', interface='socketcan')
        print("✅ 成功连接到CAN接口 vcan0")

        # 测试消息集合
        # 多组不同长度的原始数据（raw），对应 1-can.json 中的 nodeId 映射
        # 0x101: 多视角切片覆盖（全量/前4/中间4/后4）
        test_messages = [

            (0X1, [0x00], "测试1"),  
            (0x2, [0x01], "测试2"),  
            (0x3, [0x02], "测试3"),
            (0x4, [0x03], "测试4"),
                
        ]

        print("📤 开始持续发送测试消息，按 Ctrl+C 停止...")
        cycle_count = 0
        while True:
            cycle_count += 1
            for msg_id, data, description in test_messages:
                # 对于 CAN FD 帧（>8字节），自动使用 is_fd 标志
                is_fd = len(data) > 8
                msg = can.Message(
                    arbitration_id=msg_id,
                    data=data,
                    is_extended_id=False,
                    is_fd=is_fd
                )
                bus.send(msg)
                print(f"   ✅ [{cycle_count}] {description}: ID=0x{msg_id:03X}, 数据={[hex(x) for x in data]}")
                time.sleep(0.1)
            # 每轮之间稍作停顿，避免刷屏和总线占用过高
            time.sleep(5)

    except KeyboardInterrupt:
        print("🛑 接收到中断信号，停止发送。")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        if bus is not None:
            try:
                bus.shutdown()
                print("🔌 CAN接口已关闭")
            except Exception:
                pass

if __name__ == "__main__":
    quick_test()